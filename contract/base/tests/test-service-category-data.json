[{"_id": "sc_684108c2a4e5f6b2d4c2dad5", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-kube-service", "name": "Container Services", "serviceOptions": {"region": ["Europe", "North America", "Asia"], "portSpecification": ["User Specified Service Port", "Auto Assigned Port"], "persistStorage": ["Yes", "No"], "resourceUnit": ["1-Unit-Resource", "2-Unit-Resource", "4-Unit-Resource"]}, "description": "Kubernetes-based container orchestration services with flexible resource allocation", "name2ID": {"container": "container_001", "kubernetes": "k8s_001", "docker": "docker_001"}, "apiHost": "api.v-kube.service.com"}, {"_id": "sc_683fbc0eff9a6330bb9aa0b1", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-storage-service", "name": "Storage Services", "serviceOptions": {"storageType": ["SSD", "HDD", "NVMe"], "capacity": ["100GB", "500GB", "1TB", "5TB"], "redundancy": ["Single", "RAID1", "RAID5"], "encryption": ["AES256", "None"]}, "description": "Distributed storage solutions with multiple redundancy options and encryption support", "name2ID": {"storage": "storage_001", "disk": "disk_001", "volume": "volume_001"}, "apiHost": "api.v-storage.service.com"}, {"_id": "sc_683f9c656e8a8f579a2e960d", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-compute-service", "name": "Compute Services", "serviceOptions": {"cpu": ["1", "2", "4", "8", "16"], "memory": ["1GB", "2GB", "4GB", "8GB", "16GB", "32GB"], "gpu": ["None", "GTX1080", "RTX3080", "A100"], "os": ["Ubuntu20.04", "Ubuntu22.04", "CentOS7", "Windows2019"]}, "description": "High-performance computing instances with GPU acceleration support", "name2ID": {"compute": "compute_001", "vm": "vm_001", "instance": "instance_001"}, "apiHost": "api.v-compute.service.com"}, {"_id": "sc_683f9af46e8a8f579a2e960b", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-network-service", "name": "Network Services", "serviceOptions": {"bandwidth": ["100Mbps", "1Gbps", "10Gbps"], "protocol": ["TCP", "UDP", "HTTP", "HTTPS"], "loadBalancer": ["Basic", "Advanced", "Premium"], "firewall": ["Basic", "Advanced", "Enterprise"]}, "description": "Network infrastructure services including load balancing and security features", "name2ID": {"network": "network_001", "lb": "lb_001", "firewall": "fw_001"}, "apiHost": "api.v-network.service.com"}, {"_id": "sc_6830496c2422e35551ee4e01", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-database-service", "name": "Database Services", "serviceOptions": {"engine": ["MySQL", "PostgreSQL", "MongoDB", "Redis"], "version": ["5.7", "8.0", "13", "14", "4.4", "5.0", "6.2"], "storage": ["20GB", "100GB", "500GB", "1TB"], "backup": ["Daily", "Weekly", "Monthly", "None"]}, "description": "Managed database services with automated backup and scaling capabilities", "name2ID": {"database": "db_001", "mysql": "mysql_001", "postgres": "pg_001", "mongodb": "mongo_001", "redis": "redis_001"}, "apiHost": "api.v-database.service.com"}, {"_id": "sc_682f8a1b3c4d5e6f7a8b9c0d", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-ai-service", "name": "AI/ML Services", "serviceOptions": {"framework": ["TensorFlow", "PyTorch", "Scikit-learn", "<PERSON><PERSON>"], "gpu": ["V100", "A100", "H100"], "memory": ["32GB", "64GB", "128GB", "256GB"], "storage": ["1TB", "5TB", "10TB"]}, "description": "Machine learning and artificial intelligence computing services with specialized hardware", "name2ID": {"ai": "ai_001", "ml": "ml_001", "tensorflow": "tf_001", "pytorch": "pt_001"}, "apiHost": "api.v-ai.service.com"}, {"_id": "sc_682e7b2c4d5e6f7a8b9c0e1f", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-security-service", "name": "Security Services", "serviceOptions": {"encryption": ["AES128", "AES256", "RSA2048", "RSA4096"], "authentication": ["Basic", "OAuth2", "SAML", "LDAP"], "monitoring": ["Basic", "Advanced", "Enterprise"], "compliance": ["SOC2", "ISO27001", "GDPR", "HIPAA"]}, "description": "Comprehensive security services including encryption, authentication, and compliance monitoring", "name2ID": {"security": "sec_001", "encryption": "enc_001", "auth": "auth_001", "monitor": "mon_001"}, "apiHost": "api.v-security.service.com"}, {"_id": "sc_682d6c3d4e5f6a7b8c9d0e2f", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-analytics-service", "name": "Analytics Services", "serviceOptions": {"dataSource": ["SQL", "NoSQL", "Stream", "<PERSON><PERSON>"], "visualization": ["Charts", "Dashboards", "Reports", "Real-time"], "processing": ["Spark", "<PERSON><PERSON>", "Kafka", "Flink"], "storage": ["DataLake", "DataWarehouse", "TimeSeries"]}, "description": "Big data analytics and visualization services with real-time processing capabilities", "name2ID": {"analytics": "analytics_001", "bigdata": "bigdata_001", "spark": "spark_001", "hadoop": "hadoop_001"}, "apiHost": "api.v-analytics.service.com"}, {"_id": "sc_682c5d4e5f6a7b8c9d0e3f4a", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-iot-service", "name": "IoT Services", "serviceOptions": {"protocol": ["MQTT", "CoAP", "HTTP", "WebSocket"], "connectivity": ["WiFi", "Bluetooth", "LoRa", "5G"], "sensors": ["Temperature", "<PERSON><PERSON><PERSON><PERSON>", "Motion", "Light"], "processing": ["Edge", "Cloud", "Hybrid"]}, "description": "Internet of Things platform services with edge computing and sensor management", "name2ID": {"iot": "iot_001", "sensor": "sensor_001", "edge": "edge_001", "mqtt": "mqtt_001"}, "apiHost": "api.v-iot.service.com"}, {"_id": "sc_682b4e5f6a7b8c9d0e4f5a6b", "createdAt": **********, "updatedAt": **********, "deletedAt": 0, "provider": "v-blockchain-service", "name": "Blockchain Services", "serviceOptions": {"network": ["Ethereum", "Bitcoin", "Polygon", "BSC"], "consensus": ["PoW", "PoS", "DPoS", "PoA"], "smart_contracts": ["Solidity", "Vyper", "Rust", "Go"], "scaling": ["Layer1", "Layer2", "Sidechain", "State Channel"]}, "description": "Blockchain infrastructure services with smart contract deployment and scaling solutions", "name2ID": {"blockchain": "blockchain_001", "ethereum": "eth_001", "bitcoin": "btc_001", "smart_contract": "sc_001"}, "apiHost": "api.v-blockchain.service.com"}]