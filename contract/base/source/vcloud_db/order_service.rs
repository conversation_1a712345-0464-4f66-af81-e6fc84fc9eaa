// Order service module implementation

impl VCloudDB {
    /// Create a new order service from JSON string
    pub fn insert_order_service(&mut self, order_service_json: String) -> anyhow::Result<String> {
        let mut order_service: OrderService = serde_json::from_str(&order_service_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse order service JSON: {}", e))?;
        
        // Validate required fields
        if order_service._id.is_empty() {
            return Err(anyhow::anyhow!("Order service ID cannot be empty"));
        }

        if self.order_services.contains(&order_service._id) {
            return Err(anyhow::anyhow!("Order service with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_order_service_timestamp_handling(&mut order_service);

        // Ensure deleted_at is 0 for new order services
        order_service.deleted_at = 0;

        self.order_services.insert(&order_service._id, &order_service);
        Ok(order_service._id)
    }

    /// Create multiple order services in a single transaction
    ///
    /// This function enables efficient batch creation of OrderService records with:
    /// - Reduced gas costs through single transaction processing
    /// - Graceful handling of partial failures with detailed error reporting
    /// - Validation of each order service object before processing
    /// - Duplicate ID detection both within batch and against existing order services
    /// - Atomic transaction behavior for critical operations
    ///
    /// # Parameters
    /// * `order_services_json` - JSON string containing array of OrderService objects
    ///   Example: `[{"_id": "os1", "orderID": "order1", ...}, {"_id": "os2", "orderID": "order2", ...}]`
    ///
    /// # Returns
    /// JSON string containing BatchResult with:
    /// - `created`: Number of successfully created order services
    /// - `updated`: Number of updated order services (always 0 for create operations)
    /// - `deleted`: Number of deleted order services (always 0 for create operations)
    /// - `errors`: Array of error messages for failed order service creations
    ///
    /// # Error Handling
    /// - JSON parsing errors return descriptive error messages
    /// - Individual order service validation failures are collected and reported
    /// - Duplicate IDs within the batch are detected and reported
    /// - Existing order service ID conflicts are detected and reported
    ///
    /// # Example Usage
    /// ```json
    /// Input: [
    ///   {
    ///     "_id": "os_001",
    ///     "orderID": "order_001",
    ///     "userServiceID": "us_001",
    ///     "orderStatus": "pending",
    ///     "orderType": "compute"
    ///   }
    /// ]
    /// Output: {"created": 1, "updated": 0, "deleted": 0, "errors": []}
    /// ```
    pub fn insert_many_order_service(&mut self, order_services_json: String) -> anyhow::Result<String> {
        let order_services: Vec<OrderService> = serde_json::from_str(&order_services_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse order services JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Track IDs within this batch to detect duplicates
        let mut batch_ids = std::collections::HashSet::new();

        for mut order_service in order_services {
            // Validate required fields
            if order_service._id.is_empty() {
                result.errors.push("Order service ID cannot be empty".to_string());
                continue;
            }

            // Check for duplicate IDs within the batch
            if batch_ids.contains(&order_service._id) {
                result.errors.push(format!("Duplicate order service ID '{}' within batch", order_service._id));
                continue;
            }
            batch_ids.insert(order_service._id.clone());

            // Check if order service already exists
            if self.order_services.contains(&order_service._id) {
                result.errors.push(format!("Order service with ID '{}' already exists", order_service._id));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_order_service_timestamp_handling(&mut order_service);

            // Ensure deleted_at is 0 for new order services
            order_service.deleted_at = 0;

            // Insert the order service
            self.order_services.insert(&order_service._id, &order_service);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single order service by ID
    pub fn get_order_service(&self, id: String) -> anyhow::Result<String> {
        let order_service = self.order_services.get(&id);
        match order_service {
            Some(order_service) => Ok(serde_json::to_string(&order_service)?),
            None => Err(anyhow::anyhow!("Order service not found")),
        }
    }

    /// Update an existing order service from JSON string
    pub fn update_order_service(&mut self, order_service_json: String) -> anyhow::Result<()> {
        let mut order_service: OrderService = serde_json::from_str(&order_service_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse order service JSON: {}", e))?;

        // Validate required fields
        if order_service._id.is_empty() {
            return Err(anyhow::anyhow!("Order service ID cannot be empty"));
        }

        if !self.order_services.contains(&order_service._id) {
            return Err(anyhow::anyhow!("Order service not found"));
        }

        // Apply timestamp handling logic for updates
        if order_service.updated_at == 0 {
            order_service.updated_at = self.get_current_timestamp();
        }

        self.order_services.insert(&order_service._id, &order_service);
        Ok(())
    }

    /// Apply timestamp handling logic based on input values
    pub(crate) fn apply_order_service_timestamp_handling(&self, order_service: &mut OrderService) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if order_service.created_at == 0 {
            order_service.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if order_service.updated_at == 0 {
            order_service.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Update multiple order services with partial field updates
    /// Only updates non-empty specified fields, preserving existing values for unspecified fields
    pub fn update_many_order_service(&mut self, update_params_json: String) -> anyhow::Result<String> {
        let params: OrderServiceUpdate = serde_json::from_str(&update_params_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderServiceUpdate JSON: {}", e))?;

        // Find order services matching the filter criteria
        let mut order_services_to_update = Vec::new();

        if let Some(ref ids) = params.filter.ids {
            for id in ids {
                if let Some(order_service) = self.order_services.get(id) {
                    if self.matches_order_service_filters(&order_service, &params.filter) {
                        order_services_to_update.push(order_service);
                    }
                }
            }
        } else {
            let mut iter = self.order_services.index("order_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order_service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get order service: {}", e))?;
                if self.matches_order_service_filters(&order_service, &params.filter) {
                    order_services_to_update.push(order_service);
                }
            }
        }

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Update each matching order service
        for mut order_service in order_services_to_update {
            // Apply partial updates from update_data
            if let Some(order_id) = params.update_data.get("orderID").and_then(|v| v.as_str()) {
                if !order_id.is_empty() {
                    order_service.order_id = order_id.to_string();
                }
            }
            if let Some(user_service_id) = params.update_data.get("userServiceID").and_then(|v| v.as_str()) {
                if !user_service_id.is_empty() {
                    order_service.user_service_id = user_service_id.to_string();
                }
            }
            if let Some(order_status) = params.update_data.get("orderStatus").and_then(|v| v.as_str()) {
                if !order_status.is_empty() {
                    order_service.order_status = order_status.to_string();
                }
            }
            if let Some(order_type) = params.update_data.get("orderType").and_then(|v| v.as_str()) {
                if !order_type.is_empty() {
                    order_service.order_type = order_type.to_string();
                }
            }

            // Always update the updated_at timestamp
            order_service.updated_at = self.get_current_timestamp();

            self.order_services.insert(&order_service._id, &order_service);
            result.updated += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Bulk write operations for order services
    /// Supports batch operations: delete_many, insert, update
    pub fn bulk_write_order_service(&mut self, operations_json: String) -> anyhow::Result<String> {
        let operations: Vec<OrderServiceBulkWriteOperation> = serde_json::from_str(&operations_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse bulk write operations JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for operation in operations {
            match operation.operation_type.as_str() {
                "insert" => {
                    if let Some(data) = operation.data {
                        match serde_json::from_value::<OrderService>(data) {
                            Ok(mut order_service) => {
                                if order_service._id.is_empty() {
                                    result.errors.push("Order service ID cannot be empty".to_string());
                                    continue;
                                }
                                if self.order_services.contains(&order_service._id) {
                                    result.errors.push(format!("Order service with ID '{}' already exists", order_service._id));
                                    continue;
                                }
                                self.apply_order_service_timestamp_handling(&mut order_service);
                                order_service.deleted_at = 0;
                                self.order_services.insert(&order_service._id, &order_service);
                                result.created += 1;
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to parse order service data: {}", e));
                            }
                        }
                    }
                }
                "update" => {
                    if let Some(data) = operation.data {
                        match serde_json::from_value::<OrderService>(data) {
                            Ok(mut order_service) => {
                                if order_service._id.is_empty() {
                                    result.errors.push("Order service ID cannot be empty".to_string());
                                    continue;
                                }
                                if !self.order_services.contains(&order_service._id) {
                                    result.errors.push(format!("Order service with ID '{}' not found", order_service._id));
                                    continue;
                                }
                                if order_service.updated_at == 0 {
                                    order_service.updated_at = self.get_current_timestamp();
                                }
                                self.order_services.insert(&order_service._id, &order_service);
                                result.updated += 1;
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to parse order service data: {}", e));
                            }
                        }
                    }
                }
                "delete_many" => {
                    if let Some(filter) = operation.filter {
                        match self.delete_many_order_service(serde_json::to_string(&filter)?) {
                            Ok(delete_result_json) => {
                                if let Ok(delete_result) = serde_json::from_str::<BatchResult>(&delete_result_json) {
                                    result.deleted += delete_result.deleted;
                                    result.errors.extend(delete_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Delete operation failed: {}", e));
                            }
                        }
                    }
                }
                _ => {
                    result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                }
            }
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for deleting a single order service (HARD DELETE)
    pub(crate) fn delete_order_service(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: OrderServiceQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderServiceQueryParams JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.order_services.contains(id) {
                    self.order_services.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching order service
        let mut iter = self.order_services.index("order_services_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let order_service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get order service: {}", e))?;
            if self.matches_order_service_filters(&order_service, &params) {
                // Hard delete: completely remove from storage
                self.order_services.remove(&order_service._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Internal implementation for batch deleting order services
    pub(crate) fn delete_many_order_service(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: OrderServiceQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse OrderServiceQueryParams JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find order services matching the filter criteria
        let mut order_services_to_delete = Vec::new();

        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(order_service) = self.order_services.get(id) {
                    if self.matches_order_service_filters(&order_service, &params) {
                        order_services_to_delete.push(order_service);
                    }
                }
            }
        } else {
            let mut iter = self.order_services.index("order_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order_service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get order service: {}", e))?;
                if self.matches_order_service_filters(&order_service, &params) {
                    order_services_to_delete.push(order_service);
                }
            }
        }

        // Delete each matching order service (HARD DELETE)
        for order_service in order_services_to_delete {
            // Hard delete: completely remove from storage
            self.order_services.remove(&order_service._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query order services with comprehensive filtering, pagination, and sorting
    pub fn find_order_service(&self, params_json: String) -> anyhow::Result<String> {
        let params: OrderServiceQueryParams = serde_json::from_str(&params_json)?;

        let mut order_services = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Use appropriate index based on query parameters for optimal performance
        if let Some(ref ids) = params.ids {
            // Direct ID lookup - most efficient
            for id in ids {
                if let Some(order_service) = self.order_services.get(id) {
                    if self.matches_order_service_filters(&order_service, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if order_services.len() >= limit as usize {
                            break;
                        }
                        order_services.push(order_service);
                        count += 1;
                    }
                }
            }
        } else if let Some(ref user_service_id) = params.user_service_id {
            // Use user_service_id index with efficient sorting
            self.query_order_service_with_index("order_services_user_service_id", user_service_id, &params, &mut order_services, &mut count, limit, offset)?;
        } else if let Some(ref order_id) = params.order_id {
            // Use order_id index with efficient sorting
            self.query_order_service_with_index("order_services_order_id", order_id, &params, &mut order_services, &mut count, limit, offset)?;
        } else if let Some(ref order_type) = params.order_type {
            // Use order_type index with efficient sorting
            self.query_order_service_with_index("order_services_order_type", order_type, &params, &mut order_services, &mut count, limit, offset)?;
        } else if let Some(ref order_status) = params.order_status {
            // Use order_status index with efficient sorting
            self.query_order_service_with_index("order_services_order_status", order_status, &params, &mut order_services, &mut count, limit, offset)?;
        } else {
            // No specific index, use created_at index with efficient sorting
            self.query_order_service_with_created_at(&params, &mut order_services, &mut count, limit, offset)?;
        }
        // Note: Sorting is now handled efficiently during iteration, no post-processing needed
        Ok(serde_json::to_string(&order_services)?)
    }

    /// Count order services matching the given filter criteria
    pub fn count_order_service(&self, params_json: String) -> anyhow::Result<String> {
        let params: OrderServiceQueryParams = serde_json::from_str(&params_json)?;

        let mut count = 0u64;

        // Use appropriate index based on query parameters for optimal performance
        if let Some(ref ids) = params.ids {
            // Direct ID lookup - most efficient
            for id in ids {
                if let Some(order_service) = self.order_services.get(id) {
                    if self.matches_order_service_filters(&order_service, &params) {
                        count += 1;
                    }
                }
            }
        } else if let Some(ref user_service_id) = params.user_service_id {
            // Use user_service_id index
            self.count_order_service_with_index("order_services_user_service_id", user_service_id, &params, &mut count)?;
        } else if let Some(ref order_id) = params.order_id {
            // Use order_id index
            self.count_order_service_with_index("order_services_order_id", order_id, &params, &mut count)?;
        } else if let Some(ref order_type) = params.order_type {
            // Use order_type index
            self.count_order_service_with_index("order_services_order_type", order_type, &params, &mut count)?;
        } else if let Some(ref order_status) = params.order_status {
            // Use order_status index
            self.count_order_service_with_index("order_services_order_status", order_status, &params, &mut count)?;
        } else {
            // No specific index, iterate through all order services
            let mut iter = self.order_services.index("order_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order_service = iter.value()?;
                if self.matches_order_service_filters(&order_service, &params) {
                    count += 1;
                }
            }
        }

        Ok(count.to_string())
    }

    /// Helper function to check if an order service matches the given filters
    pub(crate) fn matches_order_service_filters(&self, order_service: &OrderService, params: &OrderServiceQueryParams) -> bool {
        // Skip deleted order services unless specifically querying for them
        if order_service.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&order_service._id) {
                return false;
            }
        }

        // Check order_id filter
        if let Some(ref order_id) = params.order_id {
            if order_service.order_id != *order_id {
                return false;
            }
        }

        // Check user_service_id filter
        if let Some(ref user_service_id) = params.user_service_id {
            if order_service.user_service_id != *user_service_id {
                return false;
            }
        }

        // Check order_status filter
        if let Some(ref order_status) = params.order_status {
            if order_service.order_status != *order_status {
                return false;
            }
        }

        // Check order_type filter
        if let Some(ref order_type) = params.order_type {
            if order_service.order_type != *order_type {
                return false;
            }
        }

        // Check created_at range filters
        if let Some(start) = params.created_at_start {
            if order_service.created_at < start {
                return false;
            }
        }
        if let Some(end) = params.created_at_end {
            if order_service.created_at > end {
                return false;
            }
        }

        // Check updated_at range filters
        if let Some(start) = params.updated_at_start {
            if order_service.updated_at < start {
                return false;
            }
        }
        if let Some(end) = params.updated_at_end {
            if order_service.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Efficient query using created_at index with built-in sorting
    pub(crate) fn query_order_service_with_created_at(
        &self,
        params: &OrderServiceQueryParams,
        order_services: &mut Vec<OrderService>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Set up iteration range based on sort order
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first (reverse iteration from max to min)
            (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
        } else {
            // Ascending: oldest first (forward iteration from min to max)
            (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
        };

        let mut iter = self.order_services.index("order_services_created_at").iter(reverse, &start_key, &end_key);
        while iter.next() {
            let order_service = iter.value()?;
            if self.matches_order_service_filters(&order_service, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if order_services.len() >= limit as usize {
                    break;
                }
                order_services.push(order_service);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Enhanced query with efficient index-based sorting for composite indexes
    pub(crate) fn query_order_service_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &OrderServiceQueryParams,
        order_services: &mut Vec<OrderService>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first (reverse iteration from max to min)
            (
                format!("{}-{:9>19}", key_prefix, i64::MAX),
                format!("{}-{:0>19}", key_prefix, 0),
                true,
            )
        } else {
            // Ascending: oldest first (forward iteration from min to max)
            (
                format!("{}-{:0>19}", key_prefix, 0),
                format!("{}-{:9>19}", key_prefix, i64::MAX),
                false,
            )
        };

        let mut iter = self.order_services.index(index_name).iter(reverse, &start_key, &end_key);
        while iter.next() {
            let order_service = iter.value()?;
            if self.matches_order_service_filters(&order_service, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if order_services.len() >= limit as usize {
                    break;
                }
                order_services.push(order_service);
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_order_service_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &OrderServiceQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key) =  (
            format!("{}-{:0>19}", key_prefix, 0),
            format!("{}-{:9>19}", key_prefix, i64::MAX),
        );

        let mut iter = self.order_services.index(index_name).iter(false, &start_key, &end_key);
        while iter.next() {
            let order_service = iter.value()?;
            if self.matches_order_service_filters(&order_service, &params) {
                *count += 1;
            }
        }

        Ok(())
    }
}
