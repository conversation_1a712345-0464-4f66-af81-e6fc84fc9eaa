#[cfg(test)]
mod service_type_tests {
    use super::*;
    use std::collections::HashMap;

    fn create_test_service_type(id: &str) -> ServiceType {
        let mut service_options = HashMap::new();
        service_options.insert("cpu".to_string(), vec!["1".to_string(), "2".to_string(), "4".to_string()]);
        service_options.insert("memory".to_string(), vec!["1GB".to_string(), "2GB".to_string(), "4GB".to_string()]);

        let mut charging_options = HashMap::new();
        charging_options.insert("billing_type".to_string(), "hourly".to_string());
        charging_options.insert("currency".to_string(), "USD".to_string());

        let mut duration_map = HashMap::new();
        duration_map.insert(1, 10.0);
        duration_map.insert(24, 200.0);
        duration_map.insert(168, 1200.0);

        let price_set = PriceSet {
            price: 10.0,
            charging_options,
            duration: duration_map,
        };

        let mut service_option_desc = HashMap::new();
        let mut cpu_desc = HashMap::new();
        cpu_desc.insert("1".to_string(), "1 vCPU".to_string());
        cpu_desc.insert("2".to_string(), "2 vCPU".to_string());
        cpu_desc.insert("4".to_string(), "4 vCPU".to_string());
        service_option_desc.insert("cpu".to_string(), cpu_desc);

        ServiceType {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            name: "Test Compute Service".to_string(),
            provider: "test_provider".to_string(),
            refundable: true,
            category_id: "compute_category_001".to_string(),
            category: "Compute".to_string(),
            service_options,
            description: "Test compute service for unit testing".to_string(),
            api_host: "api.test.compute.com".to_string(),
            duration_to_price: vec![price_set],
            service_option_desc,
        }
    }

    #[glue::test]
    fn test_insert_service_type() {
        let mut db = VCloudDB::new();
        let service_type = create_test_service_type("test_service_type_1");
        let service_type_json = serde_json::to_string(&service_type).unwrap();
        
        let result = db.insert_service_type(service_type_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test_service_type_1");
        
        // Verify service type was inserted
        assert!(db.service_types.contains(&"test_service_type_1".to_string()));
    }

    #[glue::test]
    fn test_get_service_type() {
        let mut db = VCloudDB::new();
        let service_type = create_test_service_type("test_service_type_2");
        let service_type_json = serde_json::to_string(&service_type).unwrap();
        
        // Insert service type first
        db.insert_service_type(service_type_json).unwrap();
        
        // Get service type
        let result = db.get_service_type("test_service_type_2".to_string());
        assert!(result.is_ok());
        
        let retrieved_service_type: ServiceType = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_service_type._id, "test_service_type_2");
        assert_eq!(retrieved_service_type.provider, "test_provider");
        assert_eq!(retrieved_service_type.name, "Test Compute Service");
    }

    #[glue::test]
    fn test_insert_many_service_type() {
        let mut db = VCloudDB::new();
        let service_types = vec![
            create_test_service_type("batch_1"),
            create_test_service_type("batch_2"),
        ];
        let service_types_json = serde_json::to_string(&service_types).unwrap();
        
        let result = db.insert_many_service_type(service_types_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
    }

    #[glue::test]
    fn test_find_service_type() {
        let mut db = VCloudDB::new();
        let service_type1 = create_test_service_type("find_test_1");
        let mut service_type2 = create_test_service_type("find_test_2");
        service_type2.provider = "different_provider".to_string();
        
        // Insert service types
        db.insert_service_type(serde_json::to_string(&service_type1).unwrap()).unwrap();
        db.insert_service_type(serde_json::to_string(&service_type2).unwrap()).unwrap();
        
        // Query by provider
        let query_params = ServiceTypeQueryParams {
            ids: None,
            name: None,
            provider: Some("test_provider".to_string()),
            category: None,
            category_id: None,
            refundable: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.find_service_type(query_json);
        assert!(result.is_ok());
        
        let service_types: Vec<ServiceType> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(service_types.len(), 1);
        assert_eq!(service_types[0]._id, "find_test_1");
    }

    #[glue::test]
    fn test_count_service_type() {
        let mut db = VCloudDB::new();
        let service_type1 = create_test_service_type("count_test_1");
        let service_type2 = create_test_service_type("count_test_2");
        
        // Insert service types
        db.insert_service_type(serde_json::to_string(&service_type1).unwrap()).unwrap();
        db.insert_service_type(serde_json::to_string(&service_type2).unwrap()).unwrap();
        
        // Count all service types
        let query_params = ServiceTypeQueryParams {
            ids: None,
            name: None,
            provider: None,
            category: None,
            category_id: None,
            refundable: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.count_service_type(query_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "2");
    }

    #[glue::test]
    fn test_update_service_type() {
        let mut db = VCloudDB::new();
        let mut service_type = create_test_service_type("update_test_1");
        let service_type_json = serde_json::to_string(&service_type).unwrap();
        
        // Insert service type first
        db.insert_service_type(service_type_json).unwrap();
        
        // Update service type
        service_type.name = "Updated Compute Service".to_string();
        service_type.description = "Updated description".to_string();
        let updated_json = serde_json::to_string(&service_type).unwrap();
        
        let result = db.update_service_type(updated_json);
        assert!(result.is_ok());
        
        // Verify update
        let retrieved = db.get_service_type("update_test_1".to_string()).unwrap();
        let retrieved_service_type: ServiceType = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_service_type.name, "Updated Compute Service");
        assert_eq!(retrieved_service_type.description, "Updated description");
    }

    #[glue::test]
    fn test_delete_service_type() {
        let mut db = VCloudDB::new();
        let service_type = create_test_service_type("delete_test_1");
        let service_type_json = serde_json::to_string(&service_type).unwrap();
        
        // Insert service type first
        db.insert_service_type(service_type_json).unwrap();
        
        // Delete service type
        let filter = ServiceTypeQueryParams {
            ids: Some(vec!["delete_test_1".to_string()]),
            name: None,
            provider: None,
            category: None,
            category_id: None,
            refundable: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let filter_json = serde_json::to_string(&filter).unwrap();
        
        let result = db.delete_service_type(filter_json);
        assert!(result.is_ok());
        
        // Verify deletion - service type should not be found
        let get_result = db.get_service_type("delete_test_1".to_string());
        assert!(get_result.is_err());
    }
}
