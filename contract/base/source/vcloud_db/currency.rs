// Currency module implementation

impl VCloudDB {
    /// Create a new currency from JSON string
    pub fn insert_currency(&mut self, currency_json: String) -> anyhow::Result<String> {
        let mut currency: Currency = serde_json::from_str(&currency_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse currency JSON: {}", e))?;
        
        // Validate required fields
        if currency._id.is_empty() {
            return Err(anyhow::anyhow!("Currency ID cannot be empty"));
        }

        if self.currencies.contains(&currency._id) {
            return Err(anyhow::anyhow!("Currency with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_currency_timestamp_handling(&mut currency);

        // Ensure deleted_at is 0 for new currencies
        currency.deleted_at = 0;

        self.currencies.insert(&currency._id, &currency);
        Ok(currency._id)
    }

    /// Create multiple currencies in a single transaction
    ///
    /// This function enables efficient batch creation of Currency records with:
    /// - Reduced gas costs through single transaction processing
    /// - Graceful handling of partial failures with detailed error reporting
    /// - Validation of each currency object before processing
    /// - Duplicate ID detection both within batch and against existing currencies
    /// - Atomic transaction behavior for critical operations
    ///
    /// # Parameters
    /// * `currencies_json` - JSON string containing array of Currency objects
    ///   Example: `[{"_id": "currency1", "nameOrId": "BTC", ...}, {"_id": "currency2", "nameOrId": "ETH", ...}]`
    ///
    /// # Returns
    /// JSON string containing BatchResult with:
    /// - `created`: Number of successfully created currencies
    /// - `updated`: Number of updated currencies (always 0 for create operations)
    /// - `deleted`: Number of deleted currencies (always 0 for create operations)
    /// - `errors`: Array of error messages for failed currency creations
    ///
    /// # Error Handling
    /// - JSON parsing errors return descriptive error messages
    /// - Individual currency validation failures are collected and reported
    /// - Duplicate IDs within the batch are detected and reported
    /// - Existing currency ID conflicts are detected and reported
    ///
    /// # Example Usage
    /// ```json
    /// [
    ///   {
    ///     "_id": "btc_001",
    ///     "nameOrId": "Bitcoin",
    ///     "contractId": "0x123...",
    ///     "symbolName": "BTC",
    ///     "contractType": "ERC20",
    ///     "unit": 8,
    ///     "exchangeRate": 45000.0
    ///   }
    /// ]
    /// ```
    pub fn insert_many_currency(&mut self, currencies_json: String) -> anyhow::Result<String> {
        let currencies: Vec<Currency> = serde_json::from_str(&currencies_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse currencies JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Track IDs within this batch to detect duplicates
        let mut batch_ids = std::collections::HashSet::new();

        for mut currency in currencies {
            // Validate ID is not empty
            if currency._id.is_empty() {
                result.errors.push("Currency ID cannot be empty".to_string());
                continue;
            }

            // Check for duplicate IDs within the batch
            if batch_ids.contains(&currency._id) {
                result.errors.push(format!("Duplicate currency ID in batch: {}", currency._id));
                continue;
            }
            batch_ids.insert(currency._id.clone());

            // Check if currency already exists
            if self.currencies.contains(&currency._id) {
                result.errors.push(format!("Currency with ID '{}' already exists", currency._id));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_currency_timestamp_handling(&mut currency);

            // Ensure deleted_at is 0 for new currencies
            currency.deleted_at = 0;

            // Insert the currency
            self.currencies.insert(&currency._id, &currency);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single currency by ID
    pub fn get_currency(&self, id: String) -> anyhow::Result<String> {
        let currency = self.currencies.get(&id);
        match currency {
            Some(currency) => Ok(serde_json::to_string(&currency)?),
            None => Err(anyhow::anyhow!("Currency not found")),
        }
    }

    /// Count currencies matching the given filter
    pub fn count_currency(&self, params_json: String) -> anyhow::Result<String> {
        let params: CurrencyQueryParams = serde_json::from_str(&params_json)?;

        let mut count = 0u64;

        // Use created_at index for efficient iteration
        let mut iter = self.currencies.index("currencies_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while iter.next() {
            let currency = iter.value()?;
            if self.matches_currency_filters(&currency, &params) {
                count += 1;
            }
        }

        Ok(count.to_string())
    }

    /// Update a single currency
    pub fn update_currency(&mut self, update_json: String) -> anyhow::Result<String> {
        let update_params: CurrencyUpdate = serde_json::from_str(&update_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse CurrencyUpdate JSON: {}", e))?;

        // Find the first matching currency
        let mut iter = self.currencies.index("currencies_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while iter.next() {
            let mut currency = iter.value()?;
            if self.matches_currency_filters(&currency, &update_params.filter) {
                // Update the currency with new data
                if let Some(new_data) = update_params.update_data.as_object() {
                    // Update specific fields based on the provided data
                    if let Some(name_or_id) = new_data.get("nameOrId").and_then(|v| v.as_str()) {
                        currency.name_or_id = name_or_id.to_string();
                    }
                    if let Some(contract_id) = new_data.get("contractId").and_then(|v| v.as_str()) {
                        currency.contract_id = contract_id.to_string();
                    }
                    if let Some(symbol_name) = new_data.get("symbolName").and_then(|v| v.as_str()) {
                        currency.symbol_name = symbol_name.to_string();
                    }
                    if let Some(contract_type) = new_data.get("contractType").and_then(|v| v.as_str()) {
                        currency.contract_type = contract_type.to_string();
                    }
                    if let Some(unit) = new_data.get("unit").and_then(|v| v.as_i64()) {
                        currency.unit = unit as i32;
                    }
                    if let Some(exchange_rate) = new_data.get("exchangeRate").and_then(|v| v.as_f64()) {
                        currency.exchange_rate = exchange_rate;
                    }
                }

                // Update timestamp
                currency.updated_at = self.get_current_timestamp();

                // Save the updated currency
                self.currencies.insert(&currency._id, &currency);
                return Ok(format!("{{\"updated\": 1}}"));
            }
        }

        Ok(format!("{{\"updated\": 0}}"))
    }

    /// Update multiple currencies matching the filter
    pub fn update_many_currency(&mut self, update_json: String) -> anyhow::Result<String> {
        let update_params: CurrencyUpdate = serde_json::from_str(&update_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse CurrencyUpdate JSON: {}", e))?;

        let mut updated_count = 0u64;
        let mut currencies_to_update = Vec::new();

        // Find all matching currencies
        let mut iter = self.currencies.index("currencies_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while iter.next() {
            let currency = iter.value()?;
            if self.matches_currency_filters(&currency, &update_params.filter) {
                currencies_to_update.push(currency);
            }
        }

        // Update each matching currency
        for mut currency in currencies_to_update {
            // Update the currency with new data
            if let Some(new_data) = update_params.update_data.as_object() {
                // Update specific fields based on the provided data
                if let Some(name_or_id) = new_data.get("nameOrId").and_then(|v| v.as_str()) {
                    currency.name_or_id = name_or_id.to_string();
                }
                if let Some(contract_id) = new_data.get("contractId").and_then(|v| v.as_str()) {
                    currency.contract_id = contract_id.to_string();
                }
                if let Some(symbol_name) = new_data.get("symbolName").and_then(|v| v.as_str()) {
                    currency.symbol_name = symbol_name.to_string();
                }
                if let Some(contract_type) = new_data.get("contractType").and_then(|v| v.as_str()) {
                    currency.contract_type = contract_type.to_string();
                }
                if let Some(unit) = new_data.get("unit").and_then(|v| v.as_i64()) {
                    currency.unit = unit as i32;
                }
                if let Some(exchange_rate) = new_data.get("exchangeRate").and_then(|v| v.as_f64()) {
                    currency.exchange_rate = exchange_rate;
                }
            }

            // Update timestamp
            currency.updated_at = self.get_current_timestamp();

            // Save the updated currency
            self.currencies.insert(&currency._id, &currency);
            updated_count += 1;
        }

        Ok(format!("{{\"updated\": {}}}", updated_count))
    }

    /// Bulk write operations for currencies
    pub fn bulk_write_currency(&mut self, operations_json: String) -> anyhow::Result<String> {
        let operations: Vec<CurrencyBulkWriteOperation> = serde_json::from_str(&operations_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse bulk write operations JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for operation in operations {
            match operation.operation_type.as_str() {
                "insert" => {
                    if let Some(data) = operation.data {
                        if let Ok(currencies) = serde_json::from_value::<Vec<Currency>>(data) {
                            for mut currency in currencies {
                                if currency._id.is_empty() {
                                    result.errors.push("Currency ID cannot be empty".to_string());
                                    continue;
                                }

                                if self.currencies.contains(&currency._id) {
                                    result.errors.push(format!("Currency with ID '{}' already exists", currency._id));
                                    continue;
                                }

                                // Apply timestamp handling logic
                                self.apply_currency_timestamp_handling(&mut currency);
                                currency.deleted_at = 0;

                                self.currencies.insert(&currency._id, &currency);
                                result.created += 1;
                            }
                        } else {
                            result.errors.push("Invalid data format for insert operation".to_string());
                        }
                    }
                }
                "update" => {
                    if let (Some(filter), Some(data)) = (operation.filter, operation.data) {
                        let mut currencies_to_update = Vec::new();

                        // Find all matching currencies
                        let mut iter = self.currencies.index("currencies_created_at").iter(
                            false,
                            &format!("{:0>19}", 0),
                            &format!("{:0>19}", i64::MAX)
                        );

                        while iter.next() {
                            let currency = iter.value()?;
                            if self.matches_currency_filters(&currency, &filter) {
                                currencies_to_update.push(currency);
                            }
                        }

                        // Update each matching currency
                        for mut currency in currencies_to_update {
                            if let Some(new_data) = data.as_object() {
                                // Update specific fields based on the provided data
                                if let Some(name_or_id) = new_data.get("nameOrId").and_then(|v| v.as_str()) {
                                    currency.name_or_id = name_or_id.to_string();
                                }
                                if let Some(contract_id) = new_data.get("contractId").and_then(|v| v.as_str()) {
                                    currency.contract_id = contract_id.to_string();
                                }
                                if let Some(symbol_name) = new_data.get("symbolName").and_then(|v| v.as_str()) {
                                    currency.symbol_name = symbol_name.to_string();
                                }
                                if let Some(contract_type) = new_data.get("contractType").and_then(|v| v.as_str()) {
                                    currency.contract_type = contract_type.to_string();
                                }
                                if let Some(unit) = new_data.get("unit").and_then(|v| v.as_i64()) {
                                    currency.unit = unit as i32;
                                }
                                if let Some(exchange_rate) = new_data.get("exchangeRate").and_then(|v| v.as_f64()) {
                                    currency.exchange_rate = exchange_rate;
                                }
                            }

                            currency.updated_at = self.get_current_timestamp();
                            self.currencies.insert(&currency._id, &currency);
                            result.updated += 1;
                        }
                    }
                }
                "delete_many" => {
                    if let Some(filter) = operation.filter {
                        let mut currencies_to_delete = Vec::new();

                        // Find all matching currencies
                        let mut iter = self.currencies.index("currencies_created_at").iter(
                            false,
                            &format!("{:0>19}", 0),
                            &format!("{:0>19}", i64::MAX)
                        );

                        while iter.next() {
                            let currency = iter.value()?;
                            if self.matches_currency_filters(&currency, &filter) {
                                currencies_to_delete.push(currency);
                            }
                        }

                        // Delete each matching currency (HARD DELETE)
                        for currency in currencies_to_delete {
                            self.currencies.remove(&currency._id);
                            result.deleted += 1;
                        }
                    }
                }
                _ => {
                    result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                }
            }
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for deleting a single currency (HARD DELETE)
    pub(crate) fn delete_currency(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: CurrencyQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CurrencyQueryParams JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.currencies.contains(id) {
                    self.currencies.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching currency
        let mut iter = self.currencies.index("currencies_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while iter.next() {
            let currency = iter.value()?;
            if self.matches_currency_filters(&currency, &params) {
                // Hard delete: completely remove from storage
                self.currencies.remove(&currency._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Internal implementation for deleting multiple currencies (HARD DELETE)
    pub(crate) fn delete_many_currency(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: CurrencyQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse CurrencyQueryParams JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        let mut currencies_to_delete = Vec::new();

        // Find all matching currencies
        let mut iter = self.currencies.index("currencies_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while iter.next() {
            let currency = iter.value()?;
            if self.matches_currency_filters(&currency, &params) {
                currencies_to_delete.push(currency);
            }
        }

        // Delete each matching currency (HARD DELETE)
        for currency in currencies_to_delete {
            // Hard delete: completely remove from storage
            self.currencies.remove(&currency._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query currencies with comprehensive filtering, pagination, and sorting
    pub fn find_currency(&self, params_json: String) -> anyhow::Result<String> {
        let params: CurrencyQueryParams = serde_json::from_str(&params_json)?;

        let mut currencies = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Use created_at index for efficient iteration
        let mut iter = self.currencies.index("currencies_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:0>19}", i64::MAX)
        );

        while iter.next() {
            let currency = iter.value()?;
            if self.matches_currency_filters(&currency, &params) {
                if count < offset {
                    count += 1;
                    continue;
                }
                if currencies.len() >= limit as usize {
                    break;
                }
                currencies.push(currency);
                count += 1;
            }
        }

        Ok(serde_json::to_string(&currencies)?)
    }

    /// Helper function to check if a currency matches the given filters
    pub(crate) fn matches_currency_filters(&self, currency: &Currency, params: &CurrencyQueryParams) -> bool {
        // Skip deleted currencies unless specifically querying for them
        if currency.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&currency._id) {
                return false;
            }
        }

        // Check nameOrId filter
        if let Some(ref name_or_id) = params.name_or_id {
            if currency.name_or_id != *name_or_id {
                return false;
            }
        }

        // Check contractId filter
        if let Some(ref contract_id) = params.contract_id {
            if currency.contract_id != *contract_id {
                return false;
            }
        }

        // Check symbolName filter
        if let Some(ref symbol_name) = params.symbol_name {
            if currency.symbol_name != *symbol_name {
                return false;
            }
        }

        // Check contractType filter
        if let Some(ref contract_type) = params.contract_type {
            if currency.contract_type != *contract_type {
                return false;
            }
        }

        // Check created_at range
        if let Some(start) = params.created_at_start {
            if currency.created_at < start {
                return false;
            }
        }
        if let Some(end) = params.created_at_end {
            if currency.created_at > end {
                return false;
            }
        }

        // Check updated_at range
        if let Some(start) = params.updated_at_start {
            if currency.updated_at < start {
                return false;
            }
        }
        if let Some(end) = params.updated_at_end {
            if currency.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Apply timestamp handling logic based on input values for Currency
    pub(crate) fn apply_currency_timestamp_handling(&self, currency: &mut Currency) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if currency.created_at == 0 {
            currency.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if currency.updated_at == 0 {
            currency.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }
}
