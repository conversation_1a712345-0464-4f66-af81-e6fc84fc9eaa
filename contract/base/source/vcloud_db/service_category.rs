impl VCloudDB {
    /// Create a new service category from JSON string
    pub fn insert_service_category(&mut self, category_json: String) -> anyhow::Result<String> {
        let mut category: ServiceCategory = serde_json::from_str(&category_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse service category JSON: {}", e))?;
        
        // Validate required fields
        if category._id.is_empty() {
            return Err(anyhow::anyhow!("Service category ID cannot be empty"));
        }

        if self.service_categories.contains(&category._id) {
            return Err(anyhow::anyhow!("Service category with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_timestamp_handling_service_category(&mut category);

        // Ensure deleted_at is 0 for new categories
        category.deleted_at = 0;

        self.service_categories.insert(&category._id, &category);
        Ok(category._id)
    }

    /// Create multiple service categories in a single transaction
    ///
    /// This function enables efficient batch creation of ServiceCategory records with:
    /// - Reduced gas costs through single transaction processing
    /// - Graceful handling of partial failures with detailed error reporting
    /// - Validation of each category object before processing
    /// - Duplicate ID detection both within batch and against existing categories
    /// - Atomic transaction behavior for critical operations
    ///
    /// # Parameters
    /// * `categories_json` - JSON string containing array of ServiceCategory objects
    ///   Example: `[{"_id": "cat1", "name": "Compute", ...}, {"_id": "cat2", "name": "Storage", ...}]`
    ///
    /// # Returns
    /// JSON string containing BatchResult with:
    /// - `created`: Number of successfully created categories
    /// - `errors`: Array of error messages for failed operations
    ///
    /// # Example Usage
    /// ```rust
    /// let categories = vec![
    ///     ServiceCategory { _id: "cat1".to_string(), name: "Compute".to_string(), ... },
    ///     ServiceCategory { _id: "cat2".to_string(), name: "Storage".to_string(), ... }
    /// ];
    /// let result = db.insert_many_service_category(serde_json::to_string(&categories)?)?;
    /// ```
    pub fn insert_many_service_category(&mut self, categories_json: String) -> anyhow::Result<String> {
        let categories: Vec<ServiceCategory> = serde_json::from_str(&categories_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse service categories JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Validate and process each category
        for mut category in categories {
            // Validate required fields
            if category._id.is_empty() {
                result.errors.push("Service category ID cannot be empty".to_string());
                continue;
            }

            // Check for duplicates
            if self.service_categories.contains(&category._id) {
                result.errors.push(format!("Service category with ID '{}' already exists", category._id));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_timestamp_handling_service_category(&mut category);

            // Ensure deleted_at is 0 for new categories
            category.deleted_at = 0;

            // Insert the category
            self.service_categories.insert(&category._id, &category);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single service category by ID
    pub fn get_service_category(&self, id: String) -> anyhow::Result<String> {
        let category = self.service_categories.get(&id);
        match category {
            Some(category) => Ok(serde_json::to_string(&category)?),
            None => Err(anyhow::anyhow!("Service category not found")),
        }
    }

    /// Update an existing service category from JSON string
    pub fn update_service_category(&mut self, category_json: String) -> anyhow::Result<()> {
        let mut category: ServiceCategory = serde_json::from_str(&category_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse service category JSON: {}", e))?;

        // Validate required fields
        if category._id.is_empty() {
            return Err(anyhow::anyhow!("Service category ID cannot be empty"));
        }

        if !self.service_categories.contains(&category._id) {
            return Err(anyhow::anyhow!("Service category not found"));
        }

        // Apply timestamp handling logic for updates
        if category.updated_at == 0 {
            category.updated_at = self.get_current_timestamp();
        }

        self.service_categories.insert(&category._id, &category);
        Ok(())
    }

    /// Apply timestamp handling logic based on input values
    pub(crate) fn apply_timestamp_handling_service_category(&self, category: &mut ServiceCategory) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if category.created_at == 0 {
            category.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if category.updated_at == 0 {
            category.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Update multiple service categories with partial field updates
    ///
    /// This function provides efficient batch updating capabilities with:
    /// - Selective field updates (only updates non-empty specified fields)
    /// - Comprehensive filtering support for targeted updates
    /// - Atomic transaction behavior ensuring data consistency
    /// - Detailed error reporting for failed operations
    /// - Optimized performance through efficient indexing
    ///
    /// # Parameters
    /// * `update_params_json` - JSON string containing ServiceCategoryUpdate with:
    ///   - `filter`: ServiceCategoryQueryParams for selecting categories to update
    ///   - `update_data`: JSON object containing fields to update
    ///
    /// # Field Update Logic
    /// - String fields: Updated only if new value is non-empty
    /// - Numeric fields: Updated only if new value is greater than 0
    /// - Boolean fields: Always updated when specified
    /// - Timestamp fields: Auto-updated for `updated_at` if not specified
    ///
    /// # Returns
    /// JSON string containing BatchResult with update statistics and errors
    ///
    /// # Example Usage
    /// ```rust
    /// let update_params = ServiceCategoryUpdate {
    ///     filter: ServiceCategoryQueryParams { provider: Some("provider1".to_string()), .. },
    ///     update_data: json!({ "description": "Updated description", "api_host": "new.api.host" })
    /// };
    /// let result = db.update_many_service_category(serde_json::to_string(&update_params)?)?;
    /// ```
    pub fn update_many_service_category(&mut self, update_params_json: String) -> anyhow::Result<String> {
        let update_params: ServiceCategoryUpdate = serde_json::from_str(&update_params_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse ServiceCategoryUpdate JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Parse the update data
        let update_category: ServiceCategory = serde_json::from_value(update_params.update_data)
            .map_err(|e| anyhow::anyhow!("Failed to parse update data: {}", e))?;

        // Find categories to update
        let mut categories_to_update = Vec::new();

        // If IDs are provided, fetch categories by IDs directly
        if let Some(ref ids) = update_params.filter.ids {
            for id in ids {
                if let Some(category) = self.service_categories.get(id) {
                    if self.matches_service_category_filters(&category, &update_params.filter) {
                        categories_to_update.push(category);
                    }
                }
            }
        } else {
            let mut iter = self.service_categories.index("service_categories_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let category = iter.value()?;
                if self.matches_service_category_filters(&category, &update_params.filter) {
                    categories_to_update.push(category);
                }
            }
        }

        // Update each matching category
        for mut category in categories_to_update {
            // Update only non-empty/valid fields from update_category
            if !update_category.provider.is_empty() {
                category.provider = update_category.provider.clone();
            }
            if !update_category.name.is_empty() {
                category.name = update_category.name.clone();
            }
            if !update_category.description.is_empty() {
                category.description = update_category.description.clone();
            }
            if !update_category.api_host.is_empty() {
                category.api_host = update_category.api_host.clone();
            }
            if !update_category.service_options.is_empty() {
                category.service_options = update_category.service_options.clone();
            }
            if !update_category.name2_id.is_empty() {
                category.name2_id = update_category.name2_id.clone();
            }

            // Always update updated_at timestamp
            category.updated_at = self.get_current_timestamp();

            // Save the updated category
            self.service_categories.insert(&category._id, &category);
            result.updated += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Bulk write operations for service categories
    ///
    /// This function supports multiple types of operations in a single transaction:
    /// - `insert`: Create new service categories
    /// - `update`: Update existing service categories
    /// - `delete_many`: Delete multiple service categories based on filters
    ///
    /// # Parameters
    /// * `operations_json` - JSON string containing array of ServiceCategoryBulkWriteOperation
    ///
    /// # Returns
    /// JSON string containing BatchResult with operation statistics
    pub fn bulk_write_service_category(&mut self, operations_json: String) -> anyhow::Result<String> {
        let operations: Vec<ServiceCategoryBulkWriteOperation> = serde_json::from_str(&operations_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse bulk write operations JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for operation in operations {
            match operation.operation_type.as_str() {
                "insert" => {
                    if let Some(data) = operation.data {
                        match serde_json::from_value::<ServiceCategory>(data) {
                            Ok(mut category) => {
                                if category._id.is_empty() {
                                    result.errors.push("Service category ID cannot be empty".to_string());
                                    continue;
                                }
                                if self.service_categories.contains(&category._id) {
                                    result.errors.push(format!("Service category with ID '{}' already exists", category._id));
                                    continue;
                                }
                                self.apply_timestamp_handling_service_category(&mut category);
                                category.deleted_at = 0;
                                self.service_categories.insert(&category._id, &category);
                                result.created += 1;
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to parse service category data: {}", e));
                            }
                        }
                    }
                }
                "update" => {
                    if let (Some(filter), Some(data)) = (operation.filter, operation.data) {
                        match serde_json::from_value::<ServiceCategory>(data) {
                            Ok(update_category) => {
                                // Find and update matching categories
                                let mut categories_to_update = Vec::new();
                                if let Some(ref ids) = filter.ids {
                                    for id in ids {
                                        if let Some(category) = self.service_categories.get(id) {
                                            if self.matches_service_category_filters(&category, &filter) {
                                                categories_to_update.push(category);
                                            }
                                        }
                                    }
                                } else {
                                    let mut iter = self.service_categories.index("service_categories_created_at").iter(
                                        false,
                                        &format!("{:0>19}", 0),
                                        &format!("{:9>19}", i64::MAX)
                                    );
                                    while iter.next() {
                                        let category = iter.value()?;
                                        if self.matches_service_category_filters(&category, &filter) {
                                            categories_to_update.push(category);
                                        }
                                    }
                                }

                                for mut category in categories_to_update {
                                    // Update fields from update_category
                                    if !update_category.provider.is_empty() {
                                        category.provider = update_category.provider.clone();
                                    }
                                    if !update_category.name.is_empty() {
                                        category.name = update_category.name.clone();
                                    }
                                    if !update_category.description.is_empty() {
                                        category.description = update_category.description.clone();
                                    }
                                    if !update_category.api_host.is_empty() {
                                        category.api_host = update_category.api_host.clone();
                                    }
                                    category.updated_at = self.get_current_timestamp();
                                    self.service_categories.insert(&category._id, &category);
                                    result.updated += 1;
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to parse update data: {}", e));
                            }
                        }
                    }
                }
                "delete_many" => {
                    if let Some(filter) = operation.filter {
                        match self.delete_many_service_category(serde_json::to_string(&filter)?) {
                            Ok(delete_result_json) => {
                                if let Ok(delete_result) = serde_json::from_str::<BatchResult>(&delete_result_json) {
                                    result.deleted += delete_result.deleted;
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to delete categories: {}", e));
                            }
                        }
                    }
                }
                _ => {
                    result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                }
            }
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for deleting a single service category (HARD DELETE)
    pub(crate) fn delete_service_category(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: ServiceCategoryQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse ServiceCategoryQueryParams JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.service_categories.contains(id) {
                    self.service_categories.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching category
        let mut iter = self.service_categories.index("service_categories_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let category = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service category: {}", e))?;
            if self.matches_service_category_filters(&category, &params) {
                // Hard delete: completely remove from storage
                self.service_categories.remove(&category._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Internal implementation for batch deleting service categories
    pub(crate) fn delete_many_service_category(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: ServiceCategoryQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse ServiceCategoryQueryParams JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find categories to delete
        let mut categories_to_delete = Vec::new();

        // If IDs are provided, fetch categories by IDs directly
        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(category) = self.service_categories.get(id) {
                    if self.matches_service_category_filters(&category, &params) {
                        categories_to_delete.push(category);
                    }
                }
            }
        } else {
            let mut iter = self.service_categories.index("service_categories_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let category = iter.value()?;
                if self.matches_service_category_filters(&category, &params) {
                    categories_to_delete.push(category);
                }
            }
        }

        // Delete each matching category (HARD DELETE)
        for category in categories_to_delete {
            // Hard delete: completely remove from storage
            self.service_categories.remove(&category._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query service categories with comprehensive filtering, pagination, and sorting
    pub fn find_service_category(&self, params_json: String) -> anyhow::Result<String> {
        let params: ServiceCategoryQueryParams = serde_json::from_str(&params_json)?;

        let mut categories = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Determine the most efficient index to use based on provided filters
        // Priority: Handle batch ID queries first, then use specific indexes when filters are present
        if let Some(ref ids) = params.ids {
            // Batch ID query - fetch categories by IDs directly
            for id in ids {
                if let Some(category) = self.service_categories.get(id) {
                    if self.matches_service_category_filters(&category, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if categories.len() >= limit as usize {
                            break;
                        }
                        categories.push(category);
                        count += 1;
                    }
                }
            }
        } else if let Some(ref provider) = params.provider {
            // Use provider index with efficient sorting
            self.query_service_category_with_index("service_categories_provider", provider, &params, &mut categories, &mut count, limit, offset)?;
        } else if let Some(ref name) = params.name {
            // Use name index with efficient sorting
            self.query_service_category_with_index("service_categories_name", name, &params, &mut categories, &mut count, limit, offset)?;
        } else {
            // No specific index, use created_at index with efficient sorting
            self.query_service_category_with_created_at(&params, &mut categories, &mut count, limit, offset)?;
        }
        // Note: Sorting is now handled efficiently during iteration, no post-processing needed
        Ok(serde_json::to_string(&categories)?)
    }

    /// Count service categories with advanced filtering
    pub fn count_service_category(&self, params_json: String) -> anyhow::Result<String> {
        let params: ServiceCategoryQueryParams = serde_json::from_str(&params_json)?;

        let mut count = 0u64;

        // Determine the most efficient index to use based on provided filters
        // Priority: Handle batch ID queries first, then use specific indexes when filters are present
        if let Some(ref ids) = params.ids {
            // Batch ID query - fetch categories by IDs directly
            for id in ids {
                if let Some(category) = self.service_categories.get(id) {
                    if self.matches_service_category_filters(&category, &params) {
                        count += 1;
                    }
                }
            }
        } else if let Some(ref provider) = params.provider {
            // Use provider index with efficient sorting
            let key_prefix = provider;
            self.count_service_category_with_index("service_categories_provider", key_prefix, &params, &mut count)?;
        } else if let Some(ref name) = params.name {
            // Use name index with efficient sorting
            let key_prefix = name;
            self.count_service_category_with_index("service_categories_name", key_prefix, &params, &mut count)?;
        } else {
            // No specific index, use created_at index
            self.count_service_category_with_created_at(&params, &mut count)?;
        }

        Ok(count.to_string())
    }

    /// Helper function to check if a service category matches the given filters
    pub(crate) fn matches_service_category_filters(&self, category: &ServiceCategory, params: &ServiceCategoryQueryParams) -> bool {
        // Skip deleted categories unless specifically querying for them
        if category.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&category._id) {
                return false;
            }
        }

        // Check provider filter
        if let Some(ref provider) = params.provider {
            if category.provider != *provider {
                return false;
            }
        }

        // Check name filter
        if let Some(ref name) = params.name {
            if category.name != *name {
                return false;
            }
        }

        // Check created_at range filters
        if let Some(start) = params.created_at_start {
            if category.created_at < start {
                return false;
            }
        }

        if let Some(end) = params.created_at_end {
            if category.created_at > end {
                return false;
            }
        }

        // Check updated_at range filters
        if let Some(start) = params.updated_at_start {
            if category.updated_at < start {
                return false;
            }
        }

        if let Some(end) = params.updated_at_end {
            if category.updated_at > end {
                return false;
            }
        }

        true
    }

    /// Efficient query using created_at index with built-in sorting
    pub(crate) fn query_service_category_with_created_at(
        &self,
        params: &ServiceCategoryQueryParams,
        categories: &mut Vec<ServiceCategory>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Set up iteration range based on sort order
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first (reverse iteration from max to min)
            (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
        } else {
            // Ascending: oldest first (forward iteration from min to max)
            (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
        };

        let mut iter = self.service_categories.index("service_categories_created_at").iter(reverse, &start_key, &end_key);
        while iter.next() {
            let category = iter.value()?;
            if self.matches_service_category_filters(&category, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if categories.len() >= limit as usize {
                    break;
                }
                categories.push(category);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Enhanced query with efficient index-based sorting for composite indexes
    pub(crate) fn query_service_category_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &ServiceCategoryQueryParams,
        categories: &mut Vec<ServiceCategory>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first
            (format!("{}-{:9>19}", key_prefix, i64::MAX), format!("{}-{:0>19}", key_prefix, 0), true)
        } else {
            // Ascending: oldest first
            (format!("{}-{:0>19}", key_prefix, 0), format!("{}-{:9>19}", key_prefix, i64::MAX), false)
        };
        let mut iter = self.service_categories.index(index_name).iter(reverse, &start_key, &end_key);
        while iter.next() {
            let category = iter.value()?;
            if self.matches_service_category_filters(&category, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if categories.len() >= limit as usize {
                    break;
                }
                categories.push(category);
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_service_category_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &ServiceCategoryQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key) =  (
            format!("{}-{:0>19}", key_prefix, 0),
            format!("{}-{:9>19}", key_prefix, i64::MAX),
        );

        let mut iter = self.service_categories.index(index_name).iter(false, &start_key, &end_key);
        while iter.next() {
            let category = iter.value()?;
            if self.matches_service_category_filters(&category, &params) {
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_service_category_with_created_at(
        &self,
        params: &ServiceCategoryQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        let mut iter = self.service_categories.index("service_categories_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let category = iter.value()?;
            if self.matches_service_category_filters(&category, &params) {
                *count += 1;
            }
        }

        Ok(())
    }
}
